#!/bin/bash

# Script setup hoàn chỉnh cho PlanBook AI System trên macOS/Linux

echo "🎯 PlanBook AI System Setup"
echo "============================"
echo ""

# Check Python installation
echo "🔍 Checking Python installation..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed"
    echo "💡 Please install Python 3.8+ using Homebrew: brew install python"
    exit 1
fi

python3 --version
echo "✅ Python is available"
echo ""

# Check pip
echo "🔍 Checking pip..."
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip is not installed"
    echo "💡 Try: python3 -m ensurepip --upgrade"
    exit 1
fi

echo "✅ pip is available"
echo ""

# Install dependencies
echo "📦 Installing Python dependencies..."
echo "This may take several minutes..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    echo "💡 Please check the error messages above"
    exit 1
fi

echo "✅ All Python dependencies installed successfully"
echo ""

# Check Redis
echo "🔍 Checking Redis connection..."
python3 -c "import redis; r = redis.Redis.from_url('redis://localhost:6379/1'); r.ping(); print('✅ Redis: Connected')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  Redis: Not running"
    echo "💡 Please start Redis:"
    echo "    - brew services start redis"
    echo "    - or use Docker: docker run -d -p 6379:6379 redis:alpine"
    echo ""
else
    echo "✅ Redis: Connected"
    echo ""
fi

# Check MongoDB
echo "🔍 Checking MongoDB connection..."
python3 -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017/'); client.admin.command('ping'); print('✅ MongoDB: Connected')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  MongoDB: Not running"
    echo "💡 Please start MongoDB:"
    echo "    - brew services start mongodb-community"
    echo "    - or use Docker: docker run -d -p 27017:27017 mongo:latest"
    echo ""
else
    echo "✅ MongoDB: Connected"
    echo ""
fi

# Test core imports
echo "🧪 Testing core imports..."
python3 -c "import fastapi, uvicorn, celery, sentence_transformers, qdrant_client, tf_keras; print('✅ All core modules imported successfully')" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "❌ Some modules failed to import"
    echo "💡 Please check the error messages above"
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "🚀 Starting PlanBook AI Services..."
echo ""

# Set environment variables
export PYTHONPATH=$PYTHONPATH:$(pwd)
export CELERY_BROKER_URL=redis://localhost:6379/1
export CELERY_RESULT_BACKEND=redis://localhost:6379/1

# Start Celery Worker
echo "⚡ Starting Celery Worker..."
gnome-terminal --title="PlanBook AI - Celery Worker" -- bash -c "python3 -m celery -A app.core.celery_app worker --loglevel=info --pool=solo --concurrency=1 --include=app.tasks.pdf_tasks --queues=pdf_queue,default --hostname=planbook_worker@%h; exec bash" &

sleep 3

# Start Celery Flower
echo "🌸 Starting Celery Flower..."
gnome-terminal --title="PlanBook AI - Celery Flower" -- bash -c "python3 -m celery -A app.core.celery_app flower --port=5555; exec bash" &

sleep 3

# Start FastAPI server
echo "🌐 Starting FastAPI Server..."
gnome-terminal --title="PlanBook AI - FastAPI Server" -- bash -c "python3 -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000; exec bash" &

echo ""
echo "✅ All services started successfully!"
echo ""
echo "📊 Access Points:"
echo "  - FastAPI Server: http://localhost:8000"
echo "  - API Documentation: http://localhost:8000/docs"
echo "  - Celery Flower: http://localhost:5555"
echo ""
echo "🎯 Service Terminals:"
echo "  - Celery Worker"
echo "  - Celery Flower"
echo "  - FastAPI Server"
echo ""
echo "💡 To stop services: Close the respective terminal windows or use Ctrl+C"
echo ""
