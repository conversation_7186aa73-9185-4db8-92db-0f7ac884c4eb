#!/bin/bash
# Script tổng hợp để khởi động toàn bộ PlanBook AI System trên macOS/Linux

echo "🎯 PlanBook AI System Startup"
echo "=============================="
echo ""

# Set environment variables
export PYTHONPATH=$PYTHONPATH:$(pwd)
export CELERY_BROKER_URL=redis://localhost:6379/1
export CELERY_RESULT_BACKEND=redis://localhost:6379/1

echo "📋 Environment Configuration:"
echo "  PYTHONPATH: $PYTHONPATH"
echo "  CELERY_BROKER_URL: $CELERY_BROKER_URL"
echo "  CELERY_RESULT_BACKEND: $CELERY_RESULT_BACKEND"
echo ""

# Check Redis
echo "🔍 Checking Redis..."
python3 -c "import redis; r = redis.Redis.from_url('redis://localhost:6379/1'); r.ping(); print('✅ Redis: OK')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Redis: NOT RUNNING"
    echo "💡 Please start Redis first (e.g. 'brew services start redis' or Docker)"
    exit 1
fi

# Check MongoDB
echo "🔍 Checking MongoDB..."
python3 -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017/'); client.admin.command('ping'); print('✅ MongoDB: OK')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ MongoDB: NOT RUNNING"
    echo "💡 Please start MongoDB first (e.g. 'brew services start mongodb-community' or Docker)"
    exit 1
fi

echo ""
echo "✅ All dependencies are running!"
echo ""

# Start Celery Worker in a new Terminal window
echo "⚡ Starting Celery Worker..."
osascript <<EOF
tell application "Terminal"
    do script "cd \"$(pwd)\" && PYTHONPATH=\"$PYTHONPATH\" CELERY_BROKER_URL=\"$CELERY_BROKER_URL\" CELERY_RESULT_BACKEND=\"$CELERY_RESULT_BACKEND\" python3 -m celery -A app.core.celery_app worker --loglevel=info --pool=threads --concurrency=4 --include=app.tasks.pdf_tasks --queues=pdf_queue,default --hostname=planbook_worker@\$(hostname)"
    set custom title of front window to "PlanBook AI - Celery Worker"
end tell
EOF

sleep 2

# Start Celery Flower in a new Terminal window
echo "🌸 Starting Celery Flower..."
osascript <<EOF
tell application "Terminal"
    do script "cd \"$(pwd)\" && PYTHONPATH=\"$PYTHONPATH\" CELERY_BROKER_URL=\"$CELERY_BROKER_URL\" CELERY_RESULT_BACKEND=\"$CELERY_RESULT_BACKEND\" python3 -m celery -A app.core.celery_app flower --port=5555"
    set custom title of front window to "PlanBook AI - Celery Flower"
end tell
EOF

sleep 2

# Start FastAPI Server in a new Terminal window
echo "🌐 Starting FastAPI Server..."
osascript <<EOF
tell application "Terminal"
    do script "cd \"$(pwd)\" && PYTHONPATH=\"$PYTHONPATH\" python3 -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    set custom title of front window to "PlanBook AI - FastAPI Server"
end tell
EOF

sleep 2

# Summary
echo ""
echo "✅ All services started successfully!"
echo ""
echo "📊 Access Points:"
echo "  - FastAPI Server:     http://localhost:8000"
echo "  - API Documentation:  http://localhost:8000/docs"
echo "  - Celery Flower:      http://localhost:5555"
echo ""
echo "🎯 Service Windows:"
echo "  - Celery Worker"
echo "  - Celery Flower"
echo "  - FastAPI Server"
echo ""
echo "💡 To stop services: Close the respective terminal windows or press Ctrl+C"
echo ""
